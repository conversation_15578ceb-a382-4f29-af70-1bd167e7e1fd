"""
Celery tasks för fakturabehandling
"""

import logging
from uuid import UUID
from celery import current_task

from app.celery_app import celery_app
from app.database import SessionLocal, set_tenant_context
from app.services.invoice_processing_service import InvoiceProcessingService
from app.models import Invoice, Session as ProcessingSession

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="process_invoice")
def process_invoice_task(self, invoice_id: str, tenant_id: str):
    """
    Celery task för att bearbeta en faktura genom hela flödet
    """
    db = SessionLocal()

    try:
        # Sätt tenant context
        set_tenant_context(db, tenant_id)

        logger.info(f"Starting invoice processing task for invoice {invoice_id}")

        # Skapa processing service
        processing_service = InvoiceProcessingService(db)

        # Kör bearbetningen (detta är en async funktion, så vi behöver köra den synkront)
        import asyncio
        result = asyncio.run(
            processing_service.process_invoice(
                invoice_id=UUID(invoice_id),
                tenant_id=UUID(tenant_id)
            )
        )

        logger.info(f"Invoice processing completed for invoice {invoice_id}: {result}")
        return result

    except Exception as e:
        logger.error(f"Invoice processing failed for invoice {invoice_id}: {e}")

        # Ensure database session is properly cleaned up
        try:
            db.rollback()
        except Exception:
            pass

        # Uppdatera task status
        if current_task:
            current_task.update_state(
                state='FAILURE',
                meta={
                    'error': str(e),
                    'invoice_id': invoice_id,
                    'tenant_id': tenant_id
                }
            )

        raise

    finally:
        # Ensure database session is properly closed
        try:
            db.close()
        except Exception as close_error:
            logger.error(f"Error closing database session: {close_error}")


@celery_app.task(bind=True, name="retry_invoice_processing")
def retry_invoice_processing_task(self, invoice_id: str, tenant_id: str, from_step: str = None):
    """
    Celery task för att återförsöka fakturabehandling från ett specifikt steg
    """
    db = SessionLocal()
    
    try:
        # Sätt tenant context
        set_tenant_context(db, tenant_id)

        logger.info(f"Retrying invoice processing for invoice {invoice_id} from step {from_step}")
        
        # Hämta session och återställ status
        from app.models import Session as ProcessingSession, Invoice
        
        session = db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == UUID(invoice_id),
            ProcessingSession.tenant_id == UUID(tenant_id)
        ).first()
        
        if not session:
            raise ValueError(f"No session found for invoice {invoice_id}")
        
        # Återställ session status
        session.status = "pending"
        session.error_message = None
        session.failed_step = None
        
        # Återställ från specifikt steg om angivet
        if from_step:
            session.current_step = from_step
            # Rensa data från misslyckade steg
            if from_step == "extrahera":
                session.extracted_data = None
                session.extracted_reasoning = None
                session.context_data = None
                session.context_reasoning = None
                session.account_data = None
                session.account_reasoning = None
                session.booking_result = None
                session.booking_reasoning = None
            elif from_step == "kontext":
                session.context_data = None
                session.context_reasoning = None
                session.account_data = None
                session.account_reasoning = None
                session.booking_result = None
                session.booking_reasoning = None
            elif from_step == "hitta_konto":
                session.account_data = None
                session.account_reasoning = None
                session.booking_result = None
                session.booking_reasoning = None
            elif from_step == "bokfora":
                session.booking_result = None
                session.booking_reasoning = None
        
        # Återställ invoice status
        invoice = db.query(Invoice).filter(Invoice.id == UUID(invoice_id)).first()
        if invoice:
            invoice.status = "processing"
            invoice.processing_error = None
        
        db.commit()
        
        # Skapa processing service och kör bearbetningen
        processing_service = InvoiceProcessingService(db)
        
        import asyncio
        result = asyncio.run(
            processing_service.process_invoice(
                invoice_id=UUID(invoice_id),
                tenant_id=UUID(tenant_id)
            )
        )
        
        logger.info(f"Invoice processing retry completed for invoice {invoice_id}: {result}")
        return result
        
    except Exception as e:
        logger.error(f"Invoice processing retry failed for invoice {invoice_id}: {e}")
        
        if current_task:
            current_task.update_state(
                state='FAILURE',
                meta={
                    'error': str(e),
                    'invoice_id': invoice_id,
                    'tenant_id': tenant_id,
                    'from_step': from_step
                }
            )
        
        raise
        
    finally:
        db.close()


@celery_app.task(bind=True, name="process_step_only")
def process_step_only_task(self, invoice_id: str, tenant_id: str, step_name: str):
    """
    Celery task för att köra endast ett specifikt steg i bearbetningen
    """
    db = SessionLocal()
    
    try:
        # Sätt tenant context
        set_tenant_context(db, tenant_id)

        logger.info(f"Processing single step '{step_name}' for invoice {invoice_id}")
        
        # Hämta session
        from app.models import Session as ProcessingSession
        
        session = db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == UUID(invoice_id),
            ProcessingSession.tenant_id == UUID(tenant_id)
        ).first()
        
        if not session:
            raise ValueError(f"No session found for invoice {invoice_id}")
        
        # Skapa processing service
        processing_service = InvoiceProcessingService(db)
        
        # Kör specifikt steg
        import asyncio
        if step_name == "extrahera":
            invoice = db.query(Invoice).filter(Invoice.id == UUID(invoice_id)).first()
            asyncio.run(processing_service._step_extrahera(session, invoice))
        elif step_name == "kontext":
            asyncio.run(processing_service._step_kontext(session))
        elif step_name == "hitta_konto":
            asyncio.run(processing_service._step_hitta_konto(session))
        elif step_name == "bokfora":
            invoice = db.query(Invoice).filter(Invoice.id == UUID(invoice_id)).first()
            asyncio.run(processing_service._step_bokfora(session, invoice))
        else:
            raise ValueError(f"Unknown step: {step_name}")
        
        result = {
            "session_id": str(session.id),
            "step_completed": step_name,
            "status": "success"
        }
        
        logger.info(f"Step '{step_name}' completed for invoice {invoice_id}")
        return result
        
    except Exception as e:
        logger.error(f"Step '{step_name}' failed for invoice {invoice_id}: {e}")
        
        if current_task:
            current_task.update_state(
                state='FAILURE',
                meta={
                    'error': str(e),
                    'invoice_id': invoice_id,
                    'tenant_id': tenant_id,
                    'step_name': step_name
                }
            )
        
        raise
        
    finally:
        db.close()
